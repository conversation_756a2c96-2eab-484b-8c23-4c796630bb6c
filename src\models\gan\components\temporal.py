"""时序组件模块 - 提供时序数据处理的组件实现

本模块实现了时序数据处理的组件，包括：
1. 时序一致性
2. 时序注意力
3. 时序卷积
4. 时序池化
5. 时序规范化
"""


import torch
from torch import nn

from src.models.base.base_module import BaseModule


class TemporalCoherence(BaseModule):
    """时序一致性模块 - 保持时序数据的连续性和平滑性"""

    def __init__(self, hidden_dim: int):
        """初始化时序一致性模块

        Args:
            hidden_dim: 隐藏维度
        """
        super().__init__("TemporalCoherence")

        # 时序一致性网络
        self.coherence_net = nn.Sequential(
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2)
        )

        # 残差连接
        self.residual = nn.Identity()

        # 层归一化
        self.layer_norm = nn.LayerNorm(hidden_dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入序列 [batch_size, seq_length, hidden_dim]

        Returns:
            torch.Tensor: 处理后的序列 [batch_size, seq_length, hidden_dim]
        """
        # 转换维度顺序 [batch_size, seq_length, hidden_dim] -> [batch_size, hidden_dim, seq_length]
        x_trans = x.transpose(1, 2)

        # 应用时序一致性网络
        coherence = self.coherence_net(x_trans)

        # 转换回原始维度顺序 [batch_size, hidden_dim, seq_length] -> [batch_size, seq_length, hidden_dim]
        coherence = coherence.transpose(1, 2)

        # 残差连接
        x = x + coherence

        # 层归一化
        x = self.layer_norm(x)

        return x


class TemporalMultiHeadWrapper(BaseModule): # Renamed from TemporalAttention
    """时序多头注意力封装模块 - 封装nn.MultiheadAttention以处理时序数据"""

    def __init__(
        self,
        hidden_dim: int,
        num_heads: int = 4,
        dropout: float = 0.1
    ):
        """初始化时序注意力模块

        Args:
            hidden_dim: 隐藏维度
            num_heads: 注意力头数
            dropout: Dropout比率
        """
        super().__init__("TemporalMultiHeadWrapper") # Updated super call

        # 多头注意力
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )

        # 残差连接
        self.residual = nn.Identity()

        # 层归一化
        self.layer_norm = nn.LayerNorm(hidden_dim)

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(
        self,
        x: torch.Tensor,
        mask: torch.Tensor | None = None
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """前向传播

        Args:
            x: 输入序列 [batch_size, seq_length, hidden_dim]
            mask: 注意力掩码 [batch_size, seq_length, seq_length]

        Returns:
            Tuple[torch.Tensor, torch.Tensor]:
                处理后的序列 [batch_size, seq_length, hidden_dim]
                注意力权重 [batch_size, num_heads, seq_length, seq_length]
        """
        # 记录输入形状和统计信息
        _batch_size, _seq_length, _ = x.shape
        self.logger.info(f"TemporalMultiHeadWrapper 输入形状: {x.shape}, "
                         f"min={x.min().item():.4e}, max={x.max().item():.4e}, "
                         f"mean={x.mean().item():.4e}, std={x.std().item():.4e}")

        # 层归一化
        x_norm = self.layer_norm(x)
        self.logger.info(f"TemporalMultiHeadWrapper 归一化后: "
                         f"min={x_norm.min().item():.4e}, max={x_norm.max().item():.4e}, "
                         f"mean={x_norm.mean().item():.4e}, std={x_norm.std().item():.4e}")

        try:
            # 应用多头注意力
            self.logger.info("TemporalMultiHeadWrapper 调用多头注意力 - 开始")
            attn_output, attn_weights = self.attention(
                query=x_norm,
                key=x_norm,
                value=x_norm,
                attn_mask=mask
            )
            self.logger.info("TemporalMultiHeadWrapper 调用多头注意力 - 完成")

            # 记录注意力输出和权重的形状
            self.logger.info(f"TemporalMultiHeadWrapper 注意力输出形状: {attn_output.shape}, "
                             f"min={attn_output.min().item():.4e}, max={attn_output.max().item():.4e}, "
                             f"mean={attn_output.mean().item():.4e}, std={attn_output.std().item():.4e}")
            self.logger.info(f"TemporalMultiHeadWrapper 注意力权重形状: {attn_weights.shape}")

            # Dropout
            attn_output = self.dropout(attn_output)

            # 残差连接
            x = x + attn_output
            self.logger.info(f"TemporalMultiHeadWrapper 残差连接后: "
                             f"min={x.min().item():.4e}, max={x.max().item():.4e}, "
                             f"mean={x.mean().item():.4e}, std={x.std().item():.4e}")

            return x, attn_weights
        except Exception as e:
            self.logger.error(f"TemporalMultiHeadWrapper 前向传播失败: {e!s}", exc_info=True)
            raise


class TemporalConvolution(BaseModule):
    """时序卷积模块 - 捕捉时序数据的局部模式"""

    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        kernel_size: int = 3,
        dilation: int = 1,
        dropout: float = 0.1
    ):
        """初始化时序卷积模块

        Args:
            input_dim: 输入维度
            output_dim: 输出维度
            kernel_size: 卷积核大小
            dilation: 扩张率
            dropout: Dropout比率
        """
        super().__init__("TemporalConvolution")

        # 计算填充大小，保持序列长度不变
        padding = (kernel_size - 1) * dilation // 2

        # 时序卷积网络
        self.conv_net = nn.Sequential(
            nn.Conv1d(
                in_channels=input_dim,
                out_channels=output_dim,
                kernel_size=kernel_size,
                padding=padding,
                dilation=dilation
            ),
            nn.LeakyReLU(0.2),
            nn.Dropout(dropout)
        )

        # 残差连接
        self.residual = nn.Conv1d(input_dim, output_dim, kernel_size=1) if input_dim != output_dim else nn.Identity()

        # 层归一化
        self.layer_norm = nn.LayerNorm(output_dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入序列 [batch_size, seq_length, input_dim]

        Returns:
            torch.Tensor: 处理后的序列 [batch_size, seq_length, output_dim]
        """
        # 转换维度顺序 [batch_size, seq_length, input_dim] -> [batch_size, input_dim, seq_length]
        x_trans = x.transpose(1, 2)

        # 应用时序卷积网络
        conv_output = self.conv_net(x_trans)

        # 应用残差连接
        residual_output = self.residual(x_trans)

        # 合并输出
        output = conv_output + residual_output

        # 转换回原始维度顺序 [batch_size, output_dim, seq_length] -> [batch_size, seq_length, output_dim]
        output = output.transpose(1, 2)

        # 层归一化
        output = self.layer_norm(output)

        return output


# OutputProjection class moved to common.py
