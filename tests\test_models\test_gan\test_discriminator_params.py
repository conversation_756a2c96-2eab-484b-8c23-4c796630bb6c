"""GAN模型参数量统计报告

此脚本用于统计和报告生成器和判别器的参数量，包括各组件的详细分解。
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.abspath('.'))

from src.models.gan.discriminator import TimeSeriesDiscriminator
from src.models.gan.generator import TimeSeriesGenerator
from src.utils.config_manager import ConfigManager


def count_parameters(model):
    """计算模型参数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def report_generator_params(generator):
    """报告生成器参数量"""
    print("\n" + "="*60)
    print("生成器参数量统计")
    print("="*60)

    total_params = count_parameters(generator)
    print(f"生成器总参数量: {total_params:,}")

    # 特征编码器
    if hasattr(generator, 'feature_encoder') and generator.feature_encoder is not None:
        encoder_params = count_parameters(generator.feature_encoder)
        print(f"  特征编码器: {encoder_params:,} ({encoder_params/total_params*100:.2f}%)")

    # 噪声处理器
    if hasattr(generator, 'noise_processor') and generator.noise_processor is not None:
        noise_params = count_parameters(generator.noise_processor)
        print(f"  噪声处理器: {noise_params:,} ({noise_params/total_params*100:.2f}%)")

    # 动态特征融合
    if hasattr(generator, 'dynamic_fusion') and generator.dynamic_fusion is not None:
        fusion_params = count_parameters(generator.dynamic_fusion)
        print(f"  动态特征融合: {fusion_params:,} ({fusion_params/total_params*100:.2f}%)")

    # 序列生成器
    if hasattr(generator, 'sequence_generator') and generator.sequence_generator is not None:
        seq_params = count_parameters(generator.sequence_generator)
        print(f"  序列生成器: {seq_params:,} ({seq_params/total_params*100:.2f}%)")

        # 序列生成器内部组件
        seq_gen = generator.sequence_generator
        if hasattr(seq_gen, 'rnn') and seq_gen.rnn is not None:
            rnn_params = count_parameters(seq_gen.rnn)
            print(f"    - RNN层: {rnn_params:,} ({rnn_params/total_params*100:.2f}%)")

        if hasattr(seq_gen, 'temporal_attention') and seq_gen.temporal_attention is not None:
            attn_params = count_parameters(seq_gen.temporal_attention)
            print(f"    - 时序注意力: {attn_params:,} ({attn_params/total_params*100:.2f}%)")

        if hasattr(seq_gen, 'temporal_coherence') and seq_gen.temporal_coherence is not None:
            coherence_params = count_parameters(seq_gen.temporal_coherence)
            print(f"    - 时序一致性: {coherence_params:,} ({coherence_params/total_params*100:.2f}%)")

        if hasattr(seq_gen, 'projection') and seq_gen.projection is not None:
            proj_params = count_parameters(seq_gen.projection)
            print(f"    - 输出投影: {proj_params:,} ({proj_params/total_params*100:.2f}%)")

    return total_params

def report_discriminator_params(discriminator):
    """报告判别器参数量"""
    print("\n" + "="*60)
    print("判别器参数量统计")
    print("="*60)

    total_params = count_parameters(discriminator)
    print(f"判别器总参数量: {total_params:,}")

    # 打印各分支参数量
    if hasattr(discriminator, 'trend_branch') and discriminator.trend_branch is not None:
        trend_params = count_parameters(discriminator.trend_branch)
        print(f"  趋势一致性分支: {trend_params:,} ({trend_params/total_params*100:.2f}%)")

    if hasattr(discriminator, 'feature_branch') and discriminator.feature_branch is not None:
        feature_params = count_parameters(discriminator.feature_branch)
        print(f"  特征相关性分支: {feature_params:,} ({feature_params/total_params*100:.2f}%)")

    if hasattr(discriminator, 'temporal_branch') and discriminator.temporal_branch is not None:
        temporal_params = count_parameters(discriminator.temporal_branch)
        print(f"  时序模式分支: {temporal_params:,} ({temporal_params/total_params*100:.2f}%)")

    # 计算其他组件参数量
    other_params = total_params
    if hasattr(discriminator, 'trend_branch') and discriminator.trend_branch is not None:
        other_params -= count_parameters(discriminator.trend_branch)
    if hasattr(discriminator, 'feature_branch') and discriminator.feature_branch is not None:
        other_params -= count_parameters(discriminator.feature_branch)
    if hasattr(discriminator, 'temporal_branch') and discriminator.temporal_branch is not None:
        other_params -= count_parameters(discriminator.temporal_branch)

    # 打印注意力机制参数量
    if hasattr(discriminator, 'temporal_attention') and discriminator.temporal_attention is not None:
        temporal_attn_params = count_parameters(discriminator.temporal_attention)
        print(f"  时序注意力: {temporal_attn_params:,} ({temporal_attn_params/total_params*100:.2f}%)")
        other_params -= temporal_attn_params

    if hasattr(discriminator, 'multi_scale_attention') and discriminator.multi_scale_attention is not None:
        multi_scale_params = count_parameters(discriminator.multi_scale_attention)
        print(f"  多尺度注意力: {multi_scale_params:,} ({multi_scale_params/total_params*100:.2f}%)")
        other_params -= multi_scale_params

    if hasattr(discriminator, 'adaptive_attention') and discriminator.adaptive_attention is not None:
        adaptive_params = count_parameters(discriminator.adaptive_attention)
        print(f"  自适应注意力: {adaptive_params:,} ({adaptive_params/total_params*100:.2f}%)")
        other_params -= adaptive_params

    if hasattr(discriminator, 'dynamic_fusion') and discriminator.dynamic_fusion is not None:
        fusion_params = count_parameters(discriminator.dynamic_fusion)
        print(f"  动态特征融合: {fusion_params:,} ({fusion_params/total_params*100:.2f}%)")
        other_params -= fusion_params

    print(f"  其他组件: {other_params:,} ({other_params/total_params*100:.2f}%)")

    return total_params

def main():
    """主函数"""
    try:
        # 加载配置
        from pathlib import Path
        config_path = Path(__file__).parent.parent.parent.parent / "config.yaml"
        config = ConfigManager.from_yaml(config_path)

        # 从配置中获取参数
        feature_dim = config.data.feature_dim if hasattr(config.data, 'feature_dim') else 240
        # 如果配置中没有feature_dim，使用一个合理的默认值
        if feature_dim is None:
            feature_dim = 240  # 使用240作为默认特征维度
        hidden_dim = config.model.hidden_dim
        discriminator_hidden_dim = config.model.discriminator['hidden_dim']

        print("GAN模型参数量统计报告")
        print("="*60)
        print("配置信息:")
        print(f"  特征维度: {feature_dim}")
        print(f"  生成器隐藏维度: {hidden_dim}")
        print(f"  判别器隐藏维度: {discriminator_hidden_dim}")

        # 初始化生成器
        print("\n正在初始化生成器...")
        generator = TimeSeriesGenerator(config, feature_dim=feature_dim)
        generator_params = report_generator_params(generator)

        # 初始化判别器
        print("\n正在初始化判别器...")
        discriminator = TimeSeriesDiscriminator(
            target_dim=1,
            condition_feature_dim=feature_dim,
            hidden_dim=discriminator_hidden_dim,
            config=config
        )
        discriminator_params = report_discriminator_params(discriminator)

        # 总结
        print("\n" + "="*60)
        print("总结")
        print("="*60)
        total_model_params = generator_params + discriminator_params
        print(f"生成器参数量: {generator_params:,}")
        print(f"判别器参数量: {discriminator_params:,}")
        print(f"模型总参数量: {total_model_params:,}")
        print(f"生成器/判别器参数比例: {generator_params/discriminator_params:.2f}:1")
        print(f"判别器/生成器参数比例: {discriminator_params/generator_params:.2f}:1")

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
