"""多尺度时序建模模块 - 提供分层时序编码和自适应时间窗口
模块路径: src/models/gan/components/multi_scale_temporal.py

优化特性:
- 分层时序编码：短期、中期、长期时序模式分别建模
- 自适应时间窗口：根据特征预测时效性动态调整注意力窗口
- 时序残差连接：保持不同时间尺度间的信息完整性
"""


import torch
from torch import nn

from src.models.base.base_module import BaseModule


class AdaptiveTimeWindow(BaseModule):
    """自适应时间窗口 - 根据特征预测时效性动态调整注意力窗口"""

    def __init__(self, feature_dim: int, max_window_size: int = 48):
        super().__init__("AdaptiveTimeWindow")
        self.feature_dim = feature_dim
        self.max_window_size = max_window_size

        # 时效性评估网络 - 评估每个特征的预测时效性
        self.timeliness_net = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, 1),
            nn.Sigmoid()  # 输出0-1的时效性分数
        )

        # 窗口大小映射网络
        self.window_mapper = nn.Sequential(
            nn.Linear(1, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )

    def forward(self, features: torch.Tensor) -> tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            features: [batch_size, seq_len, feature_dim]
        Returns:
            windowed_features: [batch_size, seq_len, feature_dim]
            window_sizes: [batch_size, seq_len, 1] - 每个时间步的有效窗口大小
        """
        _, seq_len, _ = features.shape

        # 评估每个时间步特征的时效性
        timeliness_scores = self.timeliness_net(features)  # [batch, seq_len, 1]

        # 将时效性分数映射到窗口大小
        window_ratios = self.window_mapper(timeliness_scores)  # [batch, seq_len, 1]
        window_sizes = window_ratios * self.max_window_size  # [batch, seq_len, 1]

        # 创建自适应注意力掩码
        windowed_features = self._apply_adaptive_windowing(features, window_sizes)

        return windowed_features, window_sizes

    def _apply_adaptive_windowing(self, features: torch.Tensor, window_sizes: torch.Tensor) -> torch.Tensor:
        """应用自适应窗口"""
        batch_size, seq_len, feature_dim = features.shape

        # 创建位置编码
        positions = torch.arange(seq_len, device=features.device).float().unsqueeze(0).unsqueeze(-1)
        positions = positions.expand(batch_size, seq_len, 1)

        # 计算每个位置的权重
        weights = torch.zeros_like(features)

        for i in range(seq_len):
            # 计算当前位置的有效窗口
            current_window_size = window_sizes[:, i, :].unsqueeze(1)  # [batch, 1, 1]

            # 计算距离当前位置的距离
            distances = torch.abs(positions - i)  # [batch, seq_len, 1]

            # 在窗口内的位置权重为1，窗口外权重衰减
            in_window = (distances <= current_window_size).float()
            decay_weights = torch.exp(-distances / (current_window_size + 1e-8))

            position_weights = in_window * decay_weights
            weights[:, i, :] = position_weights.squeeze(-1).unsqueeze(-1).expand(-1, -1, feature_dim)[:, i, :]

        return features * weights


class HierarchicalTemporalEncoder(BaseModule):
    """分层时序编码器 - 分别处理短期、中期、长期时序模式"""

    def __init__(self, feature_dim: int, hidden_dim: int):
        super().__init__("HierarchicalTemporalEncoder")
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim

        # 短期模式编码器 (1-3天)
        self.short_term_encoder = nn.Sequential(
            nn.Conv1d(feature_dim, hidden_dim, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1),
            nn.ReLU()
        )

        # 中期模式编码器 (1-2周) - 使用更保守的参数
        self.medium_term_encoder = nn.Sequential(
            nn.Conv1d(feature_dim, hidden_dim, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=5, padding=2),
            nn.ReLU()
        )

        # 长期模式编码器 (1个月) - 使用更保守的参数
        self.long_term_encoder = nn.Sequential(
            nn.Conv1d(feature_dim, hidden_dim, kernel_size=7, padding=3),
            nn.ReLU(),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=7, padding=3),
            nn.ReLU()
        )

        # 多尺度融合网络
        self.fusion_net = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim * 2),
            nn.ReLU(),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Tanh()
        )

        # 层归一化
        self.layer_norm = nn.LayerNorm(hidden_dim)

    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            features: [batch_size, seq_len, feature_dim]
        Returns:
            hierarchical_features: [batch_size, seq_len, hidden_dim]
        """
        # 转换维度顺序用于卷积 [batch, feature_dim, seq_len]
        features_conv = features.transpose(1, 2)

        # 分别提取不同时间尺度的特征
        short_features = self.short_term_encoder(features_conv)  # [batch, hidden_dim, seq_len]
        medium_features = self.medium_term_encoder(features_conv)  # [batch, hidden_dim, seq_len]
        long_features = self.long_term_encoder(features_conv)  # [batch, hidden_dim, seq_len]

        # 转换回原始维度顺序 [batch, seq_len, hidden_dim]
        short_features = short_features.transpose(1, 2)
        medium_features = medium_features.transpose(1, 2)
        long_features = long_features.transpose(1, 2)

        # 拼接多尺度特征
        multi_scale_features = torch.cat([short_features, medium_features, long_features], dim=-1)

        # 融合多尺度特征
        fused_features = self.fusion_net(multi_scale_features)

        # 层归一化
        output = self.layer_norm(fused_features)

        return output


class TemporalResidualConnection(BaseModule):
    """时序残差连接 - 保持不同时间尺度间的信息完整性"""

    def __init__(self, feature_dim: int):
        super().__init__("TemporalResidualConnection")
        self.feature_dim = feature_dim

        # 残差权重学习网络
        self.residual_weight_net = nn.Sequential(
            nn.Linear(feature_dim * 2, feature_dim),
            nn.ReLU(),
            nn.Linear(feature_dim, 1),
            nn.Sigmoid()
        )

        # 特征对齐网络（如果输入输出维度不同）
        self.alignment_net = nn.Linear(feature_dim, feature_dim)

    def forward(self, input_features: torch.Tensor, processed_features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            input_features: [batch_size, seq_len, feature_dim] - 原始输入特征
            processed_features: [batch_size, seq_len, feature_dim] - 处理后特征
        Returns:
            residual_features: [batch_size, seq_len, feature_dim]
        """
        # 确保维度匹配
        aligned_input = self.alignment_net(input_features)

        # 计算残差连接权重
        combined_features = torch.cat([aligned_input, processed_features], dim=-1)
        residual_weights = self.residual_weight_net(combined_features)

        # 应用加权残差连接
        output = residual_weights * aligned_input + (1 - residual_weights) * processed_features

        return output


class MultiScaleTemporalModule(BaseModule):
    """多尺度时序建模模块 - 整合所有时序优化组件"""

    def __init__(self, feature_dim: int, hidden_dim: int, max_window_size: int = 48):
        super().__init__("MultiScaleTemporalModule")
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim

        # 自适应时间窗口
        self.adaptive_window = AdaptiveTimeWindow(feature_dim, max_window_size)

        # 分层时序编码器
        self.hierarchical_encoder = HierarchicalTemporalEncoder(feature_dim, hidden_dim)

        # 时序残差连接
        self.residual_connection = TemporalResidualConnection(hidden_dim)

        # 输出投影（如果需要调整维度）
        self.output_projection = nn.Linear(hidden_dim, hidden_dim) if hidden_dim != feature_dim else nn.Identity()

    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            features: [batch_size, seq_len, feature_dim]
        Returns:
            enhanced_features: [batch_size, seq_len, hidden_dim]
        """
        # 1. 应用自适应时间窗口
        windowed_features, _ = self.adaptive_window(features)

        # 2. 分层时序编码
        hierarchical_features = self.hierarchical_encoder(windowed_features)

        # 3. 时序残差连接（需要调整输入特征维度）
        if self.feature_dim != self.hidden_dim:
            # 如果维度不匹配，先投影输入特征
            if not hasattr(self, 'input_projection'):
                self.input_projection = nn.Linear(self.feature_dim, self.hidden_dim).to(features.device)
            projected_input = self.input_projection(features)
        else:
            projected_input = features

        enhanced_features = self.residual_connection(projected_input, hierarchical_features)

        # 4. 输出投影
        output = self.output_projection(enhanced_features)

        return output
